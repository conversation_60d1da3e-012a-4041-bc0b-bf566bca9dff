# -*- coding: utf-8 -*-
"""
واجهة المستخدم الرسومية
"""

import tkinter as tk
from tkinter import ttk, messagebox, simpledialog
import pandas as pd
from typing import Optional, List, Dict, Any
from database_manager import DatabaseManager
from data_processor import DataProcessor
from excel_handler import ExcelHandler
from config import WINDOW_WIDTH_RATIO, WINDOW_HEIGHT_RATIO, STANDARD_COLUMNS, ACADEMIC_STAGES

class FinanceGUI:
    def __init__(self):
        """
        تهيئة واجهة المستخدم الرسومية
        """
        self.root = tk.Tk()
        self.root.title("برنامج إدارة البيانات المالية للطلاب")
        self.root.state('zoomed')  # تكبير النافذة لتشغل الشاشة كاملة

        # تهيئة المكونات
        self.db_manager = DatabaseManager()
        self.data_processor = DataProcessor(self.db_manager)
        self.excel_handler = ExcelHandler()

        # متغيرات البيانات
        self.current_data = pd.DataFrame()

        # إعداد الواجهة
        self.setup_ui()
        self.load_data()

    def setup_ui(self):
        """
        إعداد واجهة المستخدم
        """
        # إعداد النمط
        style = ttk.Style()
        style.theme_use('clam')

        # الإطار الرئيسي
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # شريط الأدوات العلوي
        self.create_toolbar(main_frame)

        # منطقة عرض البيانات
        self.create_data_display(main_frame)

        # شريط الحالة السفلي
        self.create_status_bar(main_frame)

    def create_toolbar(self, parent):
        """
        إنشاء شريط الأدوات
        """
        toolbar_frame = ttk.Frame(parent)
        toolbar_frame.pack(fill=tk.X, pady=(0, 10))

        # الأزرار الرئيسية
        ttk.Button(
            toolbar_frame,
            text="استيراد ملف Excel",
            command=self.import_excel,
            width=15
        ).pack(side=tk.LEFT, padx=(0, 5))

        ttk.Button(
            toolbar_frame,
            text="تصدير إلى Excel",
            command=self.export_excel,
            width=15
        ).pack(side=tk.LEFT, padx=(0, 5))

        ttk.Button(
            toolbar_frame,
            text="إنشاء قالب",
            command=self.create_template,
            width=15
        ).pack(side=tk.LEFT, padx=(0, 5))

        # فاصل
        ttk.Separator(toolbar_frame, orient=tk.VERTICAL).pack(side=tk.LEFT, fill=tk.Y, padx=10)

        ttk.Button(
            toolbar_frame,
            text="إضافة طالب",
            command=self.add_student,
            width=15
        ).pack(side=tk.LEFT, padx=(0, 5))

        ttk.Button(
            toolbar_frame,
            text="تحرير طالب",
            command=self.edit_student,
            width=15
        ).pack(side=tk.LEFT, padx=(0, 5))

        ttk.Button(
            toolbar_frame,
            text="حذف طالب",
            command=self.delete_student,
            width=15
        ).pack(side=tk.LEFT, padx=(0, 5))

        # فاصل
        ttk.Separator(toolbar_frame, orient=tk.VERTICAL).pack(side=tk.LEFT, fill=tk.Y, padx=10)

        ttk.Button(
            toolbar_frame,
            text="إدارة الرسوم",
            command=self.manage_fees,
            width=15
        ).pack(side=tk.LEFT, padx=(0, 5))

        ttk.Button(
            toolbar_frame,
            text="الإحصائيات",
            command=self.show_statistics,
            width=15
        ).pack(side=tk.LEFT, padx=(0, 5))

        # زر التحديث على اليمين
        ttk.Button(
            toolbar_frame,
            text="تحديث البيانات",
            command=self.refresh_data,
            width=15
        ).pack(side=tk.RIGHT)

    def create_data_display(self, parent):
        """
        إنشاء منطقة عرض البيانات
        """
        # إطار الجدول
        table_frame = ttk.Frame(parent)
        table_frame.pack(fill=tk.BOTH, expand=True)

        # إنشاء Treeview للجدول - عرض جميع الأعمدة
        columns = STANDARD_COLUMNS  # عرض جميع الأعمدة
        self.tree = ttk.Treeview(table_frame, columns=columns, show='headings', height=20)

        # تعيين رؤوس الأعمدة
        for col in columns:
            self.tree.heading(col, text=col, anchor=tk.CENTER)
            self.tree.column(col, width=120, anchor=tk.CENTER)

        # أشرطة التمرير
        v_scrollbar = ttk.Scrollbar(table_frame, orient=tk.VERTICAL, command=self.tree.yview)
        h_scrollbar = ttk.Scrollbar(table_frame, orient=tk.HORIZONTAL, command=self.tree.xview)

        self.tree.configure(yscrollcommand=v_scrollbar.set, xscrollcommand=h_scrollbar.set)

        # ترتيب العناصر
        self.tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        v_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        h_scrollbar.pack(side=tk.BOTTOM, fill=tk.X)

        # ربط الأحداث
        self.tree.bind('<Double-1>', self.on_item_double_click)

    def create_status_bar(self, parent):
        """
        إنشاء شريط الحالة
        """
        status_frame = ttk.Frame(parent)
        status_frame.pack(fill=tk.X, pady=(10, 0))

        self.status_label = ttk.Label(status_frame, text="جاهز", relief=tk.SUNKEN, anchor=tk.W)
        self.status_label.pack(side=tk.LEFT, fill=tk.X, expand=True)

        self.count_label = ttk.Label(status_frame, text="عدد الطلاب: 0", relief=tk.SUNKEN)
        self.count_label.pack(side=tk.RIGHT, padx=(10, 0))

    def update_status(self, message: str):
        """
        تحديث شريط الحالة
        """
        self.status_label.config(text=message)
        self.root.update_idletasks()

    def update_count(self, count: int):
        """
        تحديث عداد الطلاب
        """
        self.count_label.config(text=f"عدد الطلاب: {count}")

    def load_data(self):
        """
        تحميل البيانات من قاعدة البيانات
        """
        try:
            self.update_status("جاري تحميل البيانات...")
            self.current_data = self.db_manager.get_all_students()
            self.populate_tree()
            self.update_status("تم تحميل البيانات بنجاح")
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في تحميل البيانات: {str(e)}")
            self.update_status("خطأ في تحميل البيانات")

    def populate_tree(self):
        """
        ملء الجدول بالبيانات
        """
        # مسح البيانات الحالية
        for item in self.tree.get_children():
            self.tree.delete(item)

        if self.current_data.empty:
            self.update_count(0)
            return

        # إضافة البيانات الجديدة
        columns = list(self.tree['columns'])

        for _, row in self.current_data.iterrows():
            values = []
            for col in columns:
                # تحويل أسماء الأعمدة من قاعدة البيانات إلى الأسماء المعروضة
                db_col = self.get_db_column_name(col)
                if db_col in self.current_data.columns:
                    value = row[db_col]
                    if pd.isna(value):
                        values.append('')
                    else:
                        values.append(str(value))
                else:
                    values.append('')

            self.tree.insert('', tk.END, values=values)

        self.update_count(len(self.current_data))

    def get_db_column_name(self, display_name: str) -> str:
        """
        تحويل اسم العمود المعروض إلى اسم العمود في قاعدة البيانات
        """
        mapping = {
            'العدد الإجمالي': 'total_count',
            'السيريال': 'serial_number',
            'رسم فتح ملف': 'file_opening_fee',
            'تاريخ فتح الملف': 'file_opening_date',
            'فتح ملف مرحل': 'transferred_file_opening',
            'تاريخ فتح الملف المرحل': 'transferred_file_date',
            'الدفعة': 'batch_number',
            'المرحلة': 'academic_stage',
            'اسم الطالب': 'student_name',
            'مديونية سابقة 23/24': 'previous_debt_2324',
            'مصروفات عام 2023/2024': 'old_year_fees_2324',
            'مصروفات دراسية 2025': 'new_year_fees_2025',
            'القسط الأول': 'installment_1',
            'القسط الثاني': 'installment_2',
            'القسط الثالث': 'installment_3',
            'القسط الرابع': 'installment_4',
            'القسط الخامس': 'installment_5',
            'القسط السادس': 'installment_6',
            'القسط السابع': 'installment_7',
            'القسط الثامن': 'installment_8',
            'القسط التاسع': 'installment_9',
            'تاريخ القسط الأول': 'installment_1_date',
            'تاريخ القسط الثاني': 'installment_2_date',
            'تاريخ القسط الثالث': 'installment_3_date',
            'تاريخ القسط الرابع': 'installment_4_date',
            'تاريخ القسط الخامس': 'installment_5_date',
            'تاريخ القسط السادس': 'installment_6_date',
            'تاريخ القسط السابع': 'installment_7_date',
            'تاريخ القسط الثامن': 'installment_8_date',
            'تاريخ القسط التاسع': 'installment_9_date',
            'رقم قيد القسط الأول': 'installment_1_receipt',
            'رقم قيد القسط الثاني': 'installment_2_receipt',
            'رقم قيد القسط الثالث': 'installment_3_receipt',
            'رقم قيد القسط الرابع': 'installment_4_receipt',
            'رقم قيد القسط الخامس': 'installment_5_receipt',
            'رقم قيد القسط السادس': 'installment_6_receipt',
            'رقم قيد القسط السابع': 'installment_7_receipt',
            'رقم قيد القسط الثامن': 'installment_8_receipt',
            'رقم قيد القسط التاسع': 'installment_9_receipt',
            'إجمالي الأقساط المدفوعة': 'total_paid',
            'المتبقي': 'remaining_balance',
            'خصم إخوة': 'sibling_discount',
            'خصم كاش': 'cash_discount',
            'خصم مدرسين': 'teacher_discount',
            'خصم تحويل': 'transfer_discount',
            'خصم شخصي': 'personal_discount',
            'إجمالي الخصومات': 'total_discounts',
            'ملاحظات': 'notes'
        }
        return mapping.get(display_name, display_name.lower().replace(' ', '_'))

    def import_excel(self):
        """
        استيراد ملف Excel
        """
        try:
            self.update_status("جاري استيراد ملف Excel...")

            # استيراد الملف
            df, errors = self.excel_handler.import_excel_file()

            if errors:
                messagebox.showerror("خطأ في الاستيراد", "\n".join(errors))
                self.update_status("فشل في استيراد الملف")
                return

            if df.empty:
                messagebox.showwarning("تحذير", "الملف فارغ أو لا يحتوي على بيانات صالحة")
                self.update_status("الملف فارغ")
                return

            # معالجة البيانات
            processed_df, processing_errors = self.data_processor.process_excel_data(df)

            if processing_errors:
                error_msg = "\n".join(processing_errors)
                if messagebox.askyesno("تحذيرات في البيانات",
                                     f"تم العثور على المشاكل التالية:\n{error_msg}\n\nهل تريد المتابعة؟"):
                    pass
                else:
                    self.update_status("تم إلغاء الاستيراد")
                    return

            # تحضير البيانات للإدراج في قاعدة البيانات
            records = self.data_processor.prepare_for_database(processed_df)

            # حذف البيانات القديمة (اختياري)
            if messagebox.askyesno("تأكيد", "هل تريد حذف البيانات الموجودة واستبدالها بالبيانات الجديدة؟"):
                self.db_manager.clear_all_students()

            # إدراج البيانات الجديدة
            success_count = 0
            for record in records:
                try:
                    self.db_manager.insert_student_data(record)
                    success_count += 1
                except Exception as e:
                    print(f"خطأ في إدراج السجل: {e}")

            # تحديث العرض
            self.load_data()

            messagebox.showinfo("نجح الاستيراد", f"تم استيراد {success_count} سجل بنجاح")
            self.update_status(f"تم استيراد {success_count} سجل")

        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في استيراد الملف: {str(e)}")
            self.update_status("خطأ في الاستيراد")

    def export_excel(self):
        """
        تصدير البيانات إلى Excel
        """
        try:
            if self.current_data.empty:
                messagebox.showwarning("تحذير", "لا توجد بيانات للتصدير")
                return

            self.update_status("جاري تصدير البيانات...")

            # تحويل أسماء الأعمدة إلى الأسماء المعروضة
            display_data = self.current_data.copy()

            # تصدير الملف
            success, message = self.excel_handler.export_to_excel(display_data)

            if success:
                messagebox.showinfo("نجح التصدير", message)
                self.update_status("تم التصدير بنجاح")
            else:
                messagebox.showerror("خطأ في التصدير", message)
                self.update_status("فشل في التصدير")

        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في تصدير الملف: {str(e)}")
            self.update_status("خطأ في التصدير")

    def create_template(self):
        """
        إنشاء قالب Excel
        """
        try:
            self.update_status("جاري إنشاء قالب Excel...")

            success, message = self.excel_handler.create_template_file()

            if success:
                messagebox.showinfo("نجح إنشاء القالب", message)
                self.update_status("تم إنشاء القالب بنجاح")
            else:
                messagebox.showerror("خطأ", message)
                self.update_status("فشل في إنشاء القالب")

        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في إنشاء القالب: {str(e)}")
            self.update_status("خطأ في إنشاء القالب")

    def refresh_data(self):
        """
        تحديث البيانات
        """
        self.load_data()

    def on_item_double_click(self, event):
        """
        معالج النقر المزدوج على عنصر في الجدول
        """
        self.edit_student()

    def add_student(self):
        """
        إضافة طالب جديد
        """
        messagebox.showinfo("قريباً", "ميزة إضافة طالب جديد ستكون متاحة قريباً")

    def edit_student(self):
        """
        تحرير بيانات طالب
        """
        messagebox.showinfo("قريباً", "ميزة تحرير بيانات الطالب ستكون متاحة قريباً")

    def delete_student(self):
        """
        حذف طالب
        """
        messagebox.showinfo("قريباً", "ميزة حذف الطالب ستكون متاحة قريباً")

    def manage_fees(self):
        """
        إدارة الرسوم
        """
        messagebox.showinfo("قريباً", "ميزة إدارة الرسوم ستكون متاحة قريباً")

    def show_statistics(self):
        """
        عرض الإحصائيات
        """
        try:
            stats = self.db_manager.get_statistics()

            stats_window = tk.Toplevel(self.root)
            stats_window.title("الإحصائيات")
            stats_window.geometry("600x400")

            # إطار النص
            text_frame = ttk.Frame(stats_window)
            text_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

            # منطقة النص
            text_widget = tk.Text(text_frame, wrap=tk.WORD, font=('Arial', 12))
            scrollbar = ttk.Scrollbar(text_frame, orient=tk.VERTICAL, command=text_widget.yview)
            text_widget.configure(yscrollcommand=scrollbar.set)

            # إضافة الإحصائيات
            stats_text = f"""الإحصائيات العامة:

إجمالي عدد الطلاب: {stats['total_students']}
إجمالي المديونيات: {stats['total_debt']:,.2f} جنيه
إجمالي الأقساط المدفوعة: {stats['total_paid']:,.2f} جنيه

الإحصائيات حسب المرحلة:
"""

            for stage_stat in stats['stage_statistics']:
                stage, count, debt, paid = stage_stat
                stats_text += f"\n{stage}: {count} طالب - مديونية: {debt:,.2f} - مدفوع: {paid:,.2f}"

            text_widget.insert(tk.END, stats_text)
            text_widget.config(state=tk.DISABLED)

            text_widget.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
            scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في عرض الإحصائيات: {str(e)}")

    def run(self):
        """
        تشغيل التطبيق
        """
        self.root.mainloop()
