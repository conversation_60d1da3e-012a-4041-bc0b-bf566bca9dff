#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
برنامج مالي مبسط وفعال - يحل مشكلة الأصفار والأزرار
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import pandas as pd
import os
from typing import Optional

class SimpleFinanceApp:
    def __init__(self):
        """
        تهيئة التطبيق المبسط
        """
        self.root = tk.Tk()
        self.root.title("برنامج البيانات المالية - النسخة المبسطة والفعالة")
        
        # تحديد حجم النافذة لتشغل 90% من الشاشة
        screen_width = self.root.winfo_screenwidth()
        screen_height = self.root.winfo_screenheight()
        window_width = int(screen_width * 0.9)
        window_height = int(screen_height * 0.9)
        
        # توسيط النافذة
        x = (screen_width - window_width) // 2
        y = (screen_height - window_height) // 2
        
        self.root.geometry(f"{window_width}x{window_height}+{x}+{y}")
        
        # البيانات
        self.current_data = pd.DataFrame()
        
        # إعداد الأنماط
        self.setup_styles()
        
        # إعداد الواجهة
        self.setup_ui()
    
    def setup_styles(self):
        """
        إعداد الأنماط المحسنة
        """
        style = ttk.Style()
        style.theme_use('clam')
        
        # نمط رؤوس الأعمدة - مع إصلاح مشكلة الهوفر
        style.configure(
            "Treeview.Heading",
            background='#4a90e2',
            foreground='white',
            font=('Arial', 12, 'bold'),
            relief='raised',
            borderwidth=1,
            focuscolor='none'
        )
        
        # إصلاح مشكلة الهوفر
        style.map("Treeview.Heading",
                 background=[('active', '#3a7bc8'),
                           ('pressed', '#2a6bb8')],
                 foreground=[('active', 'white'),
                           ('pressed', 'white'),
                           ('!active', 'white')])
        
        # نمط الجدول
        style.configure(
            "Treeview",
            background='white',
            foreground='black',
            rowheight=30,
            fieldbackground='white',
            font=('Arial', 11)
        )
        
        # نمط الأزرار
        style.configure(
            "TButton",
            font=('Arial', 11)
        )
        
        style.configure(
            "Accent.TButton",
            background='#0078d4',
            foreground='white',
            font=('Arial', 11, 'bold')
        )
    
    def setup_ui(self):
        """
        إعداد واجهة المستخدم
        """
        # الإطار الرئيسي
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # شريط الأدوات
        self.create_toolbar(main_frame)
        
        # منطقة عرض البيانات
        self.create_data_display(main_frame)
        
        # شريط الحالة
        self.create_status_bar(main_frame)
    
    def create_toolbar(self, parent):
        """
        إنشاء شريط الأدوات
        """
        toolbar_frame = ttk.Frame(parent)
        toolbar_frame.pack(fill=tk.X, pady=(0, 10))
        
        # الأزرار الأساسية
        ttk.Button(toolbar_frame, text="📁 استيراد Excel", 
                  command=self.import_excel, width=20, 
                  style='Accent.TButton').pack(side=tk.LEFT, padx=(0, 5))
        
        ttk.Button(toolbar_frame, text="💾 تصدير Excel", 
                  command=self.export_excel, width=20).pack(side=tk.LEFT, padx=(0, 5))
        
        ttk.Button(toolbar_frame, text="📋 إنشاء قالب", 
                  command=self.create_template, width=20).pack(side=tk.LEFT, padx=(0, 5))
        
        ttk.Button(toolbar_frame, text="🔄 تحديث", 
                  command=self.refresh_data, width=15).pack(side=tk.RIGHT)
        
        # معلومات الملف
        self.file_info_label = ttk.Label(toolbar_frame, text="لم يتم تحميل ملف", 
                                        font=('Arial', 10, 'italic'))
        self.file_info_label.pack(side=tk.RIGHT, padx=(0, 20))
    
    def create_data_display(self, parent):
        """
        إنشاء منطقة عرض البيانات
        """
        # إطار الجدول
        table_frame = ttk.Frame(parent)
        table_frame.pack(fill=tk.BOTH, expand=True)
        
        # إنشاء Treeview
        self.tree = ttk.Treeview(table_frame, show='headings', height=20)
        
        # أشرطة التمرير
        v_scrollbar = ttk.Scrollbar(table_frame, orient=tk.VERTICAL, command=self.tree.yview)
        h_scrollbar = ttk.Scrollbar(table_frame, orient=tk.HORIZONTAL, command=self.tree.xview)
        
        self.tree.configure(yscrollcommand=v_scrollbar.set, xscrollcommand=h_scrollbar.set)
        
        # ترتيب العناصر
        self.tree.grid(row=0, column=0, sticky='nsew')
        v_scrollbar.grid(row=0, column=1, sticky='ns')
        h_scrollbar.grid(row=1, column=0, sticky='ew')
        
        # تكوين الشبكة
        table_frame.grid_rowconfigure(0, weight=1)
        table_frame.grid_columnconfigure(0, weight=1)
        
        # ربط الأحداث
        self.tree.bind('<Double-1>', self.on_item_double_click)
    
    def create_status_bar(self, parent):
        """
        إنشاء شريط الحالة
        """
        status_frame = ttk.Frame(parent)
        status_frame.pack(fill=tk.X, pady=(10, 0))
        
        self.status_label = ttk.Label(status_frame, text="جاهز", 
                                     relief=tk.SUNKEN, anchor=tk.W, font=('Arial', 10))
        self.status_label.pack(side=tk.LEFT, fill=tk.X, expand=True)
        
        self.count_label = ttk.Label(status_frame, text="عدد الصفوف: 0", 
                                    relief=tk.SUNKEN, font=('Arial', 10))
        self.count_label.pack(side=tk.RIGHT, padx=(10, 0))
    
    def import_excel(self):
        """
        استيراد ملف Excel - مبسط وفعال
        """
        try:
            self.update_status("جاري اختيار الملف...")
            
            # اختيار الملف
            file_path = filedialog.askopenfilename(
                title="اختر ملف البيانات المالية",
                filetypes=[
                    ("Excel files", "*.xlsx *.xls"),
                    ("CSV files", "*.csv"),
                    ("All files", "*.*")
                ]
            )
            
            if not file_path:
                self.update_status("لم يتم اختيار ملف")
                return
            
            self.update_status("جاري قراءة الملف...")
            
            # قراءة الملف
            if file_path.endswith('.csv'):
                df = pd.read_csv(file_path, encoding='utf-8-sig')
            else:
                df = pd.read_excel(file_path, engine='openpyxl')
            
            if df.empty:
                messagebox.showwarning("تحذير", "الملف فارغ")
                self.update_status("الملف فارغ")
                return
            
            # معالجة البيانات
            self.update_status("جاري معالجة البيانات...")
            
            # تنظيف البيانات - استبدال القيم الفارغة بـ "غير محدد"
            for col in df.columns:
                if df[col].dtype == 'object':  # أعمدة نصية
                    df[col] = df[col].fillna('غير محدد')
                else:  # أعمدة رقمية
                    df[col] = df[col].fillna('غير محدد')
            
            # حفظ البيانات
            self.current_data = df
            
            # عرض البيانات
            self.display_data()
            
            # تحديث معلومات الملف
            file_name = os.path.basename(file_path)
            self.file_info_label.config(text=f"الملف: {file_name}")
            
            # رسالة النجاح
            messagebox.showinfo("نجح الاستيراد", 
                              f"تم استيراد {len(df)} صف و {len(df.columns)} عمود بنجاح!\n\n"
                              f"الملف: {file_name}")
            
            self.update_status(f"تم استيراد {len(df)} صف بنجاح")
            
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في استيراد الملف:\n{str(e)}")
            self.update_status("خطأ في الاستيراد")
    
    def display_data(self):
        """
        عرض البيانات في الجدول
        """
        if self.current_data.empty:
            return
        
        # مسح البيانات الحالية
        for item in self.tree.get_children():
            self.tree.delete(item)
        
        # تحديد الأعمدة
        columns = list(self.current_data.columns)
        self.tree['columns'] = columns
        
        # تعيين رؤوس الأعمدة
        for col in columns:
            self.tree.heading(col, text=col, anchor=tk.CENTER)
            self.tree.column(col, width=150, anchor=tk.CENTER)
        
        # إضافة البيانات
        for idx, row in self.current_data.iterrows():
            values = []
            for col in columns:
                value = row[col]
                if pd.isna(value):
                    values.append('غير محدد')
                else:
                    values.append(str(value))
            
            # ألوان متناوبة
            tag = 'evenrow' if idx % 2 == 0 else 'oddrow'
            self.tree.insert('', tk.END, values=values, tags=(tag,))
        
        # تطبيق الألوان
        self.tree.tag_configure('evenrow', background='white')
        self.tree.tag_configure('oddrow', background='#f0f0f0')
        
        # تحديث العداد
        self.count_label.config(text=f"عدد الصفوف: {len(self.current_data)}")
    
    def export_excel(self):
        """
        تصدير البيانات إلى Excel
        """
        try:
            if self.current_data.empty:
                messagebox.showwarning("تحذير", "لا توجد بيانات للتصدير")
                return
            
            # اختيار مكان الحفظ
            file_path = filedialog.asksaveasfilename(
                title="حفظ ملف Excel",
                defaultextension=".xlsx",
                filetypes=[("Excel files", "*.xlsx"), ("All files", "*.*")]
            )
            
            if not file_path:
                return
            
            self.update_status("جاري تصدير البيانات...")
            
            # حفظ الملف
            self.current_data.to_excel(file_path, index=False, engine='openpyxl')
            
            messagebox.showinfo("نجح التصدير", f"تم حفظ الملف بنجاح:\n{file_path}")
            self.update_status("تم التصدير بنجاح")
            
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في تصدير الملف:\n{str(e)}")
            self.update_status("خطأ في التصدير")
    
    def create_template(self):
        """
        إنشاء قالب Excel
        """
        try:
            # اختيار مكان الحفظ
            file_path = filedialog.asksaveasfilename(
                title="حفظ قالب Excel",
                defaultextension=".xlsx",
                filetypes=[("Excel files", "*.xlsx"), ("All files", "*.*")]
            )
            
            if not file_path:
                return
            
            # إنشاء قالب بالأعمدة الأساسية
            template_columns = [
                'اسم الطالب', 'المرحلة', 'مديونية سابقة 23/24', 
                'مصروفات عام 2023/2024', 'مصروفات دراسية 2025',
                'القسط الأول', 'القسط الثاني', 'القسط الثالث',
                'خصم إخوة', 'خصم كاش', 'ملاحظات'
            ]
            
            template_df = pd.DataFrame(columns=template_columns)
            
            # إضافة صف مثال
            example_row = {
                'اسم الطالب': 'مثال على اسم الطالب',
                'المرحلة': 'الابتدائية',
                'مديونية سابقة 23/24': 1000,
                'مصروفات عام 2023/2024': 15000,
                'مصروفات دراسية 2025': 18000,
                'القسط الأول': 2000,
                'القسط الثاني': 2000,
                'القسط الثالث': 0,
                'خصم إخوة': 500,
                'خصم كاش': 0,
                'ملاحظات': 'مثال على الملاحظات'
            }
            
            template_df = pd.concat([template_df, pd.DataFrame([example_row])], ignore_index=True)
            
            # حفظ القالب
            template_df.to_excel(file_path, index=False, engine='openpyxl')
            
            messagebox.showinfo("نجح إنشاء القالب", f"تم إنشاء القالب بنجاح:\n{file_path}")
            self.update_status("تم إنشاء القالب بنجاح")
            
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في إنشاء القالب:\n{str(e)}")
            self.update_status("خطأ في إنشاء القالب")
    
    def refresh_data(self):
        """
        تحديث عرض البيانات
        """
        self.display_data()
        self.update_status("تم تحديث العرض")
    
    def on_item_double_click(self, event):
        """
        معالج النقر المزدوج
        """
        selection = self.tree.selection()
        if selection:
            item = self.tree.item(selection[0])
            values = item['values']
            if values and len(values) > 0:
                messagebox.showinfo("تفاصيل الصف", f"البيانات: {values[:3]}...")
    
    def update_status(self, message: str):
        """
        تحديث شريط الحالة
        """
        self.status_label.config(text=message)
        self.root.update_idletasks()
    
    def run(self):
        """
        تشغيل التطبيق
        """
        self.root.mainloop()

def main():
    """
    تشغيل التطبيق المبسط
    """
    print("🚀 تشغيل برنامج البيانات المالية المبسط...")
    app = SimpleFinanceApp()
    app.run()

if __name__ == "__main__":
    main()
