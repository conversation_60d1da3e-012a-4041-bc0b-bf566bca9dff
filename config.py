# -*- coding: utf-8 -*-
"""
إعدادات البرنامج وتخطيط الأعمدة
"""

# رؤوس الأعمدة الموحدة
STANDARD_COLUMNS = [
    'العدد الإجمالي',
    'السيريال', 
    'رسم فتح ملف',
    'تاريخ فتح الملف',
    'فتح ملف مرحل',
    'تاريخ فتح الملف المرحل',
    'الدفعة',
    'المرحلة',
    'اسم الطالب',
    'مديونية سابقة 23/24',
    'مصروفات عام 2023/2024',
    'مصروفات دراسية 2025',
    'القسط الأول',
    'تاريخ القسط الأول',
    'رقم قيد القسط الأول',
    'القسط الثاني',
    'تاريخ القسط الثاني',
    'رقم قيد القسط الثاني',
    'القسط الثالث',
    'تاريخ القسط الثالث',
    'رقم قيد القسط الثالث',
    'القسط الرابع',
    'تاريخ القسط الرابع',
    'رقم قيد القسط الرابع',
    'القسط الخامس',
    'تاريخ القسط الخامس',
    'رقم قيد القسط الخامس',
    'القسط السادس',
    'تاريخ القسط السادس',
    'رقم قيد القسط السادس',
    'القسط السابع',
    'تاريخ القسط السابع',
    'رقم قيد القسط السابع',
    'القسط الثامن',
    'تاريخ القسط الثامن',
    'رقم قيد القسط الثامن',
    'القسط التاسع',
    'تاريخ القسط التاسع',
    'رقم قيد القسط التاسع',
    'إجمالي الأقساط المدفوعة',
    'المتبقي',
    'خصم إخوة',
    'خصم كاش',
    'خصم مدرسين',
    'خصم تحويل',
    'خصم شخصي',
    'إجمالي الخصومات',
    'ملاحظات'
]

# تخطيط الأعمدة - أسماء بديلة لكل عمود
COLUMN_MAPPING = {
    'اسم الطالب': ['اسم الطالب', 'Student Name', 'الاسم', 'Name', 'student_name'],
    'المرحلة': ['المرحلة', 'Academic Stage', 'المرحلة الدراسية', 'Stage', 'academic_stage'],
    'مديونية سابقة 23/24': ['مديونية سابقة 23/24', 'Previous Debt', 'مديونية سابقة', 'previous_debt'],
    'مصروفات عام 2023/2024': ['مصروفات عام 2023/2024', 'Old Year Fees', 'مصروفات سابقة', 'old_fees'],
    'مصروفات دراسية 2025': ['مصروفات دراسية 2025', 'New Year Fees', 'مصروفات جديدة', 'new_fees'],
    'القسط الأول': ['القسط الأول', 'First Installment', 'installment_1', 'قسط 1'],
    'القسط الثاني': ['القسط الثاني', 'Second Installment', 'installment_2', 'قسط 2'],
    'القسط الثالث': ['القسط الثالث', 'Third Installment', 'installment_3', 'قسط 3'],
    'القسط الرابع': ['القسط الرابع', 'Fourth Installment', 'installment_4', 'قسط 4'],
    'القسط الخامس': ['القسط الخامس', 'Fifth Installment', 'installment_5', 'قسط 5'],
    'القسط السادس': ['القسط السادس', 'Sixth Installment', 'installment_6', 'قسط 6'],
    'القسط السابع': ['القسط السابع', 'Seventh Installment', 'installment_7', 'قسط 7'],
    'القسط الثامن': ['القسط الثامن', 'Eighth Installment', 'installment_8', 'قسط 8'],
    'القسط التاسع': ['القسط التاسع', 'Ninth Installment', 'installment_9', 'قسط 9'],
    'خصم إخوة': ['خصم إخوة', 'Sibling Discount', 'sibling_discount'],
    'خصم كاش': ['خصم كاش', 'Cash Discount', 'cash_discount'],
    'خصم مدرسين': ['خصم مدرسين', 'Teacher Discount', 'teacher_discount'],
    'خصم تحويل': ['خصم تحويل', 'Transfer Discount', 'transfer_discount'],
    'خصم شخصي': ['خصم شخصي', 'Personal Discount', 'personal_discount'],
    'ملاحظات': ['ملاحظات', 'Notes', 'notes', 'تعليقات']
}

# إعدادات قاعدة البيانات
DATABASE_NAME = 'school_finance.db'

# إعدادات الواجهة
WINDOW_WIDTH_RATIO = 0.9  # 90% من عرض الشاشة
WINDOW_HEIGHT_RATIO = 0.9  # 90% من ارتفاع الشاشة

# إعدادات افتراضية
DEFAULT_FEE_INCREASE = 0.10  # زيادة 10% افتراضية

# المراحل الدراسية المتاحة
ACADEMIC_STAGES = [
    'الابتدائية',
    'الإعدادية', 
    'الثانوية',
    'رياض الأطفال'
]

# رسوم افتراضية لكل مرحلة
DEFAULT_FEES = {
    'رياض الأطفال': 15000,
    'الابتدائية': 18000,
    'الإعدادية': 20000,
    'الثانوية': 25000
}

# أعمدة الأقساط
INSTALLMENT_COLUMNS = [
    'القسط الأول', 'القسط الثاني', 'القسط الثالث', 'القسط الرابع', 'القسط الخامس',
    'القسط السادس', 'القسط السابع', 'القسط الثامن', 'القسط التاسع'
]

# أعمدة الخصومات
DISCOUNT_COLUMNS = [
    'خصم إخوة', 'خصم كاش', 'خصم مدرسين', 'خصم تحويل', 'خصم شخصي'
]

# أعمدة التواريخ
DATE_COLUMNS = [
    'تاريخ فتح الملف', 'تاريخ فتح الملف المرحل',
    'تاريخ القسط الأول', 'تاريخ القسط الثاني', 'تاريخ القسط الثالث',
    'تاريخ القسط الرابع', 'تاريخ القسط الخامس', 'تاريخ القسط السادس',
    'تاريخ القسط السابع', 'تاريخ القسط الثامن', 'تاريخ القسط التاسع'
]

# أعمدة الأرقام
NUMERIC_COLUMNS = [
    'العدد الإجمالي', 'رسم فتح ملف', 'فتح ملف مرحل', 'مديونية سابقة 23/24',
    'مصروفات عام 2023/2024', 'مصروفات دراسية 2025', 'إجمالي الأقساط المدفوعة',
    'المتبقي', 'إجمالي الخصومات'
] + INSTALLMENT_COLUMNS + DISCOUNT_COLUMNS
