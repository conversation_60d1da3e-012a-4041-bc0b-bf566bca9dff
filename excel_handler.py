# -*- coding: utf-8 -*-
"""
التعامل مع ملفات Excel
"""

import pandas as pd
import os
from typing import Tuple, List, Optional
from tkinter import filedialog, messagebox
from config import STANDARD_COLUMNS

class ExcelHandler:
    def __init__(self):
        """
        تهيئة معالج ملفات Excel
        """
        self.supported_extensions = ['.xlsx', '.xls', '.csv']
    
    def import_excel_file(self, file_path: Optional[str] = None) -> Tuple[pd.DataFrame, List[str]]:
        """
        استيراد ملف Excel
        """
        errors = []
        
        try:
            # إذا لم يتم تحديد مسار الملف، اطلب من المستخدم اختياره
            if not file_path:
                file_path = filedialog.askopenfilename(
                    title="اختر ملف Excel",
                    filetypes=[
                        ("Excel files", "*.xlsx *.xls"),
                        ("CSV files", "*.csv"),
                        ("All files", "*.*")
                    ]
                )
            
            if not file_path:
                return pd.DataFrame(), ["لم يتم اختيار ملف"]
            
            # التحقق من وجود الملف
            if not os.path.exists(file_path):
                return pd.DataFrame(), [f"الملف غير موجود: {file_path}"]
            
            # التحقق من امتداد الملف
            file_extension = os.path.splitext(file_path)[1].lower()
            if file_extension not in self.supported_extensions:
                return pd.DataFrame(), [f"نوع الملف غير مدعوم: {file_extension}"]
            
            # قراءة الملف
            if file_extension == '.csv':
                df = pd.read_csv(file_path, encoding='utf-8-sig')
            else:
                df = pd.read_excel(file_path, engine='openpyxl')
            
            # التحقق من وجود بيانات
            if df.empty:
                return pd.DataFrame(), ["الملف فارغ"]
            
            # التحقق من وجود أعمدة
            if len(df.columns) == 0:
                return pd.DataFrame(), ["الملف لا يحتوي على أعمدة"]
            
            # إزالة الصفوف الفارغة تماماً
            df = df.dropna(how='all')
            
            if df.empty:
                return pd.DataFrame(), ["الملف لا يحتوي على بيانات صالحة"]
            
            return df, []
            
        except FileNotFoundError:
            return pd.DataFrame(), [f"الملف غير موجود: {file_path}"]
        except PermissionError:
            return pd.DataFrame(), [f"لا يمكن الوصول إلى الملف: {file_path}"]
        except pd.errors.EmptyDataError:
            return pd.DataFrame(), ["الملف فارغ أو تالف"]
        except Exception as e:
            return pd.DataFrame(), [f"خطأ في قراءة الملف: {str(e)}"]
    
    def export_to_excel(self, df: pd.DataFrame, file_path: Optional[str] = None) -> Tuple[bool, str]:
        """
        تصدير البيانات إلى ملف Excel
        """
        try:
            # إذا لم يتم تحديد مسار الملف، اطلب من المستخدم اختياره
            if not file_path:
                file_path = filedialog.asksaveasfilename(
                    title="حفظ ملف Excel",
                    defaultextension=".xlsx",
                    filetypes=[
                        ("Excel files", "*.xlsx"),
                        ("CSV files", "*.csv"),
                        ("All files", "*.*")
                    ]
                )
            
            if not file_path:
                return False, "لم يتم اختيار مسار للحفظ"
            
            # التحقق من وجود بيانات للتصدير
            if df.empty:
                return False, "لا توجد بيانات للتصدير"
            
            # تحديد نوع الملف
            file_extension = os.path.splitext(file_path)[1].lower()
            
            if file_extension == '.csv':
                df.to_csv(file_path, index=False, encoding='utf-8-sig')
            else:
                # إنشاء ملف Excel مع تنسيق
                with pd.ExcelWriter(file_path, engine='openpyxl') as writer:
                    df.to_excel(writer, sheet_name='البيانات المالية', index=False)
                    
                    # الحصول على ورقة العمل لتطبيق التنسيق
                    worksheet = writer.sheets['البيانات المالية']
                    
                    # تطبيق تنسيق على رؤوس الأعمدة
                    from openpyxl.styles import Font, PatternFill, Alignment
                    
                    header_font = Font(bold=True, color="FFFFFF")
                    header_fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")
                    header_alignment = Alignment(horizontal="center", vertical="center")
                    
                    for col_num, column_title in enumerate(df.columns, 1):
                        cell = worksheet.cell(row=1, column=col_num)
                        cell.font = header_font
                        cell.fill = header_fill
                        cell.alignment = header_alignment
                    
                    # ضبط عرض الأعمدة
                    for column in worksheet.columns:
                        max_length = 0
                        column_letter = column[0].column_letter
                        
                        for cell in column:
                            try:
                                if len(str(cell.value)) > max_length:
                                    max_length = len(str(cell.value))
                            except:
                                pass
                        
                        adjusted_width = min(max_length + 2, 50)
                        worksheet.column_dimensions[column_letter].width = adjusted_width
            
            return True, f"تم حفظ الملف بنجاح: {file_path}"
            
        except PermissionError:
            return False, f"لا يمكن الكتابة في الملف: {file_path}. تأكد من أن الملف غير مفتوح في برنامج آخر"
        except Exception as e:
            return False, f"خطأ في حفظ الملف: {str(e)}"
    
    def create_template_file(self, file_path: Optional[str] = None) -> Tuple[bool, str]:
        """
        إنشاء ملف قالب Excel بالأعمدة المطلوبة
        """
        try:
            # إذا لم يتم تحديد مسار الملف، اطلب من المستخدم اختياره
            if not file_path:
                file_path = filedialog.asksaveasfilename(
                    title="حفظ قالب Excel",
                    defaultextension=".xlsx",
                    filetypes=[("Excel files", "*.xlsx"), ("All files", "*.*")]
                )
            
            if not file_path:
                return False, "لم يتم اختيار مسار للحفظ"
            
            # إنشاء DataFrame فارغ بالأعمدة المطلوبة
            template_df = pd.DataFrame(columns=STANDARD_COLUMNS)
            
            # إضافة صف مثال
            example_row = {
                'العدد الإجمالي': 1,
                'السيريال': 'S001',
                'رسم فتح ملف': 500,
                'تاريخ فتح الملف': '2024-01-01',
                'فتح ملف مرحل': 0,
                'تاريخ فتح الملف المرحل': '',
                'الدفعة': 'دفعة 2024',
                'المرحلة': 'الابتدائية',
                'اسم الطالب': 'مثال على اسم الطالب',
                'مديونية سابقة 23/24': 1000,
                'مصروفات عام 2023/2024': 15000,
                'مصروفات دراسية 2025': 18000,
                'القسط الأول': 2000,
                'تاريخ القسط الأول': '2024-02-01',
                'رقم قيد القسط الأول': 'R001',
                'القسط الثاني': 2000,
                'تاريخ القسط الثاني': '2024-03-01',
                'رقم قيد القسط الثاني': 'R002',
                'القسط الثالث': 0,
                'تاريخ القسط الثالث': '',
                'رقم قيد القسط الثالث': '',
                'القسط الرابع': 0,
                'تاريخ القسط الرابع': '',
                'رقم قيد القسط الرابع': '',
                'القسط الخامس': 0,
                'تاريخ القسط الخامس': '',
                'رقم قيد القسط الخامس': '',
                'القسط السادس': 0,
                'تاريخ القسط السادس': '',
                'رقم قيد القسط السادس': '',
                'القسط السابع': 0,
                'تاريخ القسط السابع': '',
                'رقم قيد القسط السابع': '',
                'القسط الثامن': 0,
                'تاريخ القسط الثامن': '',
                'رقم قيد القسط الثامن': '',
                'القسط التاسع': 0,
                'تاريخ القسط التاسع': '',
                'رقم قيد القسط التاسع': '',
                'إجمالي الأقساط المدفوعة': 4000,
                'المتبقي': 15000,
                'خصم إخوة': 500,
                'خصم كاش': 0,
                'خصم مدرسين': 0,
                'خصم تحويل': 0,
                'خصم شخصي': 0,
                'إجمالي الخصومات': 500,
                'ملاحظات': 'مثال على الملاحظات'
            }
            
            template_df = pd.concat([template_df, pd.DataFrame([example_row])], ignore_index=True)
            
            # حفظ القالب
            success, message = self.export_to_excel(template_df, file_path)
            
            if success:
                return True, f"تم إنشاء قالب Excel بنجاح: {file_path}"
            else:
                return False, message
                
        except Exception as e:
            return False, f"خطأ في إنشاء القالب: {str(e)}"
    
    def validate_excel_structure(self, df: pd.DataFrame) -> Tuple[bool, List[str]]:
        """
        التحقق من هيكل ملف Excel
        """
        warnings = []
        
        # التحقق من وجود أعمدة أساسية
        essential_columns = ['اسم الطالب', 'المرحلة']
        missing_essential = []
        
        for col in essential_columns:
            found = False
            for df_col in df.columns:
                if col.lower() in df_col.lower():
                    found = True
                    break
            if not found:
                missing_essential.append(col)
        
        if missing_essential:
            warnings.append(f"الأعمدة الأساسية المفقودة: {', '.join(missing_essential)}")
        
        # التحقق من وجود بيانات في الأعمدة الأساسية
        for col in essential_columns:
            for df_col in df.columns:
                if col.lower() in df_col.lower():
                    if df[df_col].isna().all():
                        warnings.append(f"العمود '{df_col}' فارغ تماماً")
                    break
        
        # التحقق من تكرار أسماء الطلاب
        name_columns = [col for col in df.columns if 'اسم' in col.lower() or 'name' in col.lower()]
        if name_columns:
            name_col = name_columns[0]
            duplicates = df[df.duplicated(subset=[name_col], keep=False)]
            if not duplicates.empty:
                warnings.append(f"يوجد {len(duplicates)} طالب بأسماء مكررة")
        
        return len(warnings) == 0, warnings
